import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import winston from 'winston';
import dotenv from 'dotenv';
import { SecurityAgent } from './core/SecurityAgent';
import { WeaviateMemory } from './memory/WeaviateMemory';
import { KafkaCommunication } from './communication/KafkaCommunication';
import { SecurityAgentConfig } from './types';

// Charger les variables d'environnement
dotenv.config();

// Configuration du logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'agent-security' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Configuration de l'agent
const agentConfig: SecurityAgentConfig = {
  id: process.env.AGENT_ID || 'agent-security-001',
  name: 'Agent Security - Advanced Security & Compliance',
  version: '1.0.0',
  capabilities: [
    'vulnerability-scanning',
    'penetration-testing',
    'code-analysis',
    'dependency-scanning',
    'container-scanning',
    'infrastructure-scanning',
    'compliance-checking',
    'threat-intelligence',
    'incident-response',
    'security-monitoring',
    'malware-detection',
    'network-security',
    'web-security',
    'api-security',
    'data-protection'
  ],
  scanners: [
    {
      name: 'SAST Scanner',
      type: 'sast',
      enabled: true,
      configuration: {
        languages: ['javascript', 'typescript', 'python', 'java', 'php', 'csharp'],
        depth: 'comprehensive',
        customRules: true
      },
      priority: 'high'
    },
    {
      name: 'DAST Scanner',
      type: 'dast',
      enabled: true,
      configuration: {
        crawling: true,
        authentication: true,
        forms: true,
        apis: true
      },
      priority: 'high'
    },
    {
      name: 'SCA Scanner',
      type: 'sca',
      enabled: true,
      configuration: {
        packageManagers: ['npm', 'pip', 'maven', 'gradle', 'composer'],
        vulnerabilityDatabases: ['nvd', 'snyk', 'github-advisory']
      },
      priority: 'medium'
    },
    {
      name: 'Container Scanner',
      type: 'container',
      enabled: true,
      configuration: {
        registries: ['docker-hub', 'gcr', 'ecr'],
        layers: true,
        secrets: true,
        malware: true
      },
      priority: 'high'
    },
    {
      name: 'Infrastructure Scanner',
      type: 'infrastructure',
      enabled: true,
      configuration: {
        cloudProviders: ['aws', 'gcp', 'azure'],
        kubernetes: true,
        terraform: true
      },
      priority: 'medium'
    }
  ],
  compliance: {
    frameworks: [
      {
        name: 'OWASP Top 10',
        version: '2021',
        enabled: true,
        controls: []
      },
      {
        name: 'CIS Controls',
        version: '8.0',
        enabled: true,
        controls: []
      },
      {
        name: 'NIST Cybersecurity Framework',
        version: '1.1',
        enabled: true,
        controls: []
      }
    ],
    customPolicies: [],
    reporting: {
      formats: ['json', 'html', 'pdf'],
      schedule: '0 0 * * 0', // Weekly
      recipients: [],
      retention: 365
    }
  },
  threatIntelligence: {
    sources: [
      {
        name: 'AlienVault OTX',
        type: 'community',
        url: 'https://otx.alienvault.com/api/v1',
        apiKey: process.env.OTX_API_KEY || '',
        enabled: !!process.env.OTX_API_KEY,
        updateFrequency: '0 */12 * * *'
      },
      {
        name: 'VirusTotal',
        type: 'commercial',
        url: 'https://www.virustotal.com/vtapi/v2',
        apiKey: process.env.VIRUSTOTAL_API_KEY || '',
        enabled: !!process.env.VIRUSTOTAL_API_KEY,
        updateFrequency: '0 */4 * * *'
      }
    ],
    feeds: [],
    indicators: [],
    analysis: {
      correlation: true,
      attribution: false,
      prediction: true,
      hunting: true,
      sandbox: {
        enabled: false,
        type: 'cuckoo',
        timeout: 300,
        environments: ['windows10', 'ubuntu20']
      }
    }
  },
  monitoring: {
    realTime: true,
    alerting: {
      enabled: true,
      channels: [
        {
          type: 'webhook',
          configuration: {
            url: process.env.WEBHOOK_URL || 'http://cortex-central:8080/security/alerts'
          },
          enabled: true
        }
      ],
      rules: [],
      escalation: []
    },
    logging: {
      level: 'info',
      destinations: [
        {
          type: 'file',
          configuration: { path: 'logs/security.log' },
          enabled: true
        }
      ],
      retention: 90,
      encryption: false
    },
    metrics: {
      collection: true,
      retention: 30,
      aggregation: ['count', 'avg', 'max'],
      export: [
        {
          type: 'prometheus',
          configuration: { port: 9090 },
          enabled: true
        }
      ]
    },
    dashboards: [
      {
        name: 'Security Overview',
        type: 'grafana',
        url: process.env.GRAFANA_URL || 'http://grafana:3000',
        enabled: true
      }
    ]
  }
};

// Initialisation de l'application Express
const app = express();
const port = process.env.PORT || 3007;

// Middleware de sécurité renforcé
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "ws:", "wss:"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Middleware CORS
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

// Middleware de parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Middleware de logging des requêtes
app.use((req, res, next) => {
  logger.info('Requête reçue', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// Variables globales pour les services
let securityAgent: SecurityAgent;
let memory: WeaviateMemory;
let communication: KafkaCommunication;

/**
 * Initialisation des services
 */
async function initializeServices(): Promise<void> {
  try {
    logger.info('🔒 Initialisation des services de l\'Agent Security...');

    // Initialiser la mémoire Weaviate
    memory = new WeaviateMemory(
      logger,
      process.env.WEAVIATE_URL || 'http://weaviate:8080'
    );

    // Initialiser la communication Kafka
    communication = new KafkaCommunication(
      logger,
      process.env.KAFKA_BROKERS || 'kafka:9092',
      agentConfig.id
    );

    // Initialiser l'agent de sécurité
    securityAgent = new SecurityAgent(
      agentConfig,
      logger,
      memory,
      communication
    );

    // Démarrage des services
    await memory.initialize();
    await communication.initialize();
    await securityAgent.initialize();

    logger.info('✅ Services de sécurité initialisés avec succès');

  } catch (error) {
    logger.error('❌ Erreur lors de l\'initialisation des services de sécurité:', error);
    throw error;
  }
}

// Routes de santé et statut

/**
 * Endpoint de santé
 */
app.get('/health', (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    agent: {
      id: agentConfig.id,
      name: agentConfig.name,
      version: agentConfig.version
    },
    services: {
      memory: memory?.getConnectionStatus() || false,
      communication: communication?.getConnectionStatus() || false,
      securityAgent: securityAgent !== undefined
    },
    capabilities: agentConfig.capabilities,
    scanners: agentConfig.scanners.filter(s => s.enabled).map(s => s.name),
    uptime: process.uptime(),
    memory: {
      used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
    }
  };

  const statusCode = health.services.memory && health.services.communication ? 200 : 503;
  res.status(statusCode).json(health);
});

/**
 * Endpoint de disponibilité
 */
app.get('/ready', (req, res) => {
  const ready = securityAgent && memory?.getConnectionStatus() && communication?.getConnectionStatus();
  res.status(ready ? 200 : 503).json({
    ready,
    timestamp: new Date().toISOString()
  });
});

/**
 * Informations sur l'agent
 */
app.get('/api/info', (req, res) => {
  res.json({
    agent: agentConfig,
    capabilities: agentConfig.capabilities,
    scanners: agentConfig.scanners,
    compliance: agentConfig.compliance.frameworks,
    threatIntelligence: agentConfig.threatIntelligence.sources.filter(s => s.enabled),
    version: agentConfig.version,
    timestamp: new Date().toISOString()
  });
});

/**
 * Statut détaillé de l'agent
 */
app.get('/api/status', (req, res) => {
  try {
    const status = securityAgent?.getStatus() || {
      isInitialized: false,
      activeScanQueue: 0,
      completedScans: 0,
      activeIncidents: 0,
      threatIndicators: 0,
      capabilities: agentConfig.capabilities,
      uptime: process.uptime()
    };

    res.json({
      success: true,
      status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération du statut:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération du statut',
      message: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    });
  }
});

// Routes API principales

/**
 * Demande de scan de sécurité
 */
app.post('/api/scan', async (req, res) => {
  try {
    const scanRequest = req.body;

    if (!scanRequest || !scanRequest.type || !scanRequest.target) {
      return res.status(400).json({
        error: 'Requête de scan invalide',
        required: ['type', 'target'],
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Demande de scan de sécurité', {
      type: scanRequest.type,
      target: scanRequest.target.type,
      priority: scanRequest.priority
    });

    // Ajout d'un ID unique si non fourni
    if (!scanRequest.id) {
      scanRequest.id = `scan-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    // Ajout du demandeur
    scanRequest.requestedBy = req.ip || 'unknown';
    scanRequest.timestamp = new Date();

    await securityAgent.handleScanRequest(scanRequest);

    res.json({
      success: true,
      scanId: scanRequest.id,
      message: 'Scan de sécurité mis en queue',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la demande de scan:', error);
    res.status(500).json({
      error: 'Erreur lors de la demande de scan',
      message: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Démarrage du serveur
 */
async function startServer(): Promise<void> {
  try {
    // Initialiser les services
    await initializeServices();

    // Démarrer le serveur HTTP
    const server = app.listen(port, () => {
      logger.info(`🔒 Agent Security démarré sur le port ${port}`, {
        agentId: agentConfig.id,
        version: agentConfig.version,
        environment: process.env.NODE_ENV || 'development',
        capabilities: agentConfig.capabilities.length
      });
    });

    // Gestion gracieuse de l'arrêt
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Signal ${signal} reçu, arrêt gracieux en cours...`);

      server.close(async () => {
        try {
          if (securityAgent) {
            await securityAgent.shutdown();
          }
          if (communication) {
            await communication.disconnect();
          }
          if (memory) {
            await memory.close();
          }
          logger.info('Arrêt gracieux terminé');
          process.exit(0);
        } catch (error) {
          logger.error('Erreur lors de l\'arrêt gracieux:', error);
          process.exit(1);
        }
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Erreur lors du démarrage du serveur Security:', error);
    process.exit(1);
  }
}

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
  logger.error('Exception non capturée', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Promesse rejetée non gérée', { reason, promise });
  process.exit(1);
});

// Démarrer l'application
startServer().catch((error) => {
  logger.error('Erreur fatale lors du démarrage Security:', error);
  process.exit(1);
});

export { app, securityAgent, memory, communication };
